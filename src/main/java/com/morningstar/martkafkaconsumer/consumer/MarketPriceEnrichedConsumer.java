package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;


@Slf4j
public class MarketPriceEnrichedConsumer extends AbstractMessageConsumer<Long, MarketPriceEnrichedEvent> {
    public MarketPriceEnrichedConsumer(KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> kafkaConsumerEndpoint) {
        super(kafkaConsumerEndpoint);
    }
    @Override
    protected void processMessages(Collection<MarketPriceEnrichedEvent> messages, String pollId) {
        log.info("MarketPriceEnrichedConsumer Processing {} messages with pollId: {}", messages.size(), pollId);
    }
}
