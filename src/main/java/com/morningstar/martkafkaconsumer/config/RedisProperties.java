package com.morningstar.martkafkaconsumer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "redis")
public class RedisProperties {
    
    private Redis redis = new Redis();
    private String clientName;
    
    @Data
    public static class Redis {
        private TsData tsData = new TsData();
        
        @Data
        public static class TsData {
            private String host;
            private int port;
        }
    }
    
    // Getter methods for backward compatibility and cleaner access
    public Redis getRedis() {
        return redis;
    }
    
    public String getClientName() {
        return clientName;
    }
}
