package com.morningstar.martkafkaconsumer.config;

import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.dp.messaging.common.impl.KafkaFacade;
import com.morningstar.martkafkaconsumer.consumer.KafkaConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.KafkaPluginConsumerEndpoint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.avro.specific.SpecificRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConsumerConfig<K, V extends SpecificRecord> {

    @Bean
    @ConditionalOnMissingBean(IKafkaFactory.class)
    public IKafkaFactory<K, V> kafkaPluginFactory(KafkaProperties kafkaProperties) {
        KafkaFacade<K, V> kafkaFacade = new KafkaFacade<>(
                kafkaProperties.getRegion(),
                kafkaProperties.getEnvironment()
        );
        return kafkaFacade.getKafkaFactory();
    }


    @Bean
    @ConditionalOnMissingBean(name = "kafkaConsumerEndpoints")
    public Map<String, KafkaConsumerEndpoint<K, V>> kafkaConsumerEndpoints(
            IKafkaFactory<K, V> kafkaFactory,
            KafkaProperties consumerTopicsProperties) {

        Map<String, KafkaConsumerEndpoint<K, V>> endpoints = new HashMap<>();

        consumerTopicsProperties.getConsumerTopics().forEach((name, config) -> {
            KafkaConsumerEndpoint<K, V> endpoint = createKafkaConsumerEndpoint(kafkaFactory, config);
            endpoints.put(name, endpoint);
        });

        return endpoints;
    }

    private KafkaConsumerEndpoint<K, V> createKafkaConsumerEndpoint(
            IKafkaFactory<K, V> kafkaFactory,
            KafkaProperties.ConsumerTopicConfig config) {

        return new KafkaPluginConsumerEndpoint<>(
                kafkaFactory,
                config.getTopic(),
                config.getSerdePojoClass(),
                config.getClientId(),
                config.getGroupId()
        );
    }


    @Data
    @Configuration
    @ConfigurationProperties(prefix = "kafka")
    public static class KafkaProperties {
        private String region ;
        private String environment;
        private String clientPrincipal;
        private String keytabRef;
        private Map<String, ConsumerTopicConfig> consumerTopics;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ConsumerTopicConfig {
            private String topic;
            private String clientId;
            private String groupId;
            private String keyDeserializer;
            private String valueDeserializer;
            private String serdePojoClass;
        }
    }



}