package com.morningstar.martkafkaconsumer.config;

import com.morningstar.data.domain.eod.events.MarketPriceDetail;
import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.martkafkaconsumer.consumer.KafkaConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.KafkaPluginConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceRawConsumer;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.util.RedisUtil;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.Delay;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.specific.SpecificRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@DependsOn("kafkaConsumerEndpoints")
public class MartKafkaConsumerAutoConfiguration<K, V extends SpecificRecord> {

    @Bean("marketPriceRawConsumerEndpoint")
    @ConditionalOnMissingBean(name = "marketPriceRawConsumerEndpoint")
    public KafkaConsumerEndpoint<K, V> marketPriceRawConsumerEndpoint(
            @Qualifier("kafkaConsumerEndpoints") Map<String, KafkaConsumerEndpoint<K, V>> kafkaConsumerEndpoints) {

        KafkaConsumerEndpoint<K, V> endpoint = kafkaConsumerEndpoints.get("market_price_list_updated");
        if (endpoint == null) {
            throw new IllegalStateException("Consumer endpoint 'market_price_list_updated' not found in configuration");
        }

        log.info("Created market_price_list_updated consumer endpoint for topic: {}", endpoint.getWorkflow());
        return endpoint;
    }

    @Bean("marketPriceEnrichedConsumerEndpoint")
    @ConditionalOnMissingBean(name = "marketPriceEnrichedConsumerEndpoint")
    public KafkaConsumerEndpoint<K, V> marketPriceEnrichedConsumerEndpoint(
            @Qualifier("kafkaConsumerEndpoints") Map<String, KafkaConsumerEndpoint<K, V>> kafkaConsumerEndpoints) {
        KafkaConsumerEndpoint<K, V> endpoint = kafkaConsumerEndpoints.get("market_price_enriched_event");
        if (endpoint == null) {
            throw new IllegalStateException("Consumer endpoint 'market_price_enriched_event' not found in configuration");
        }
        log.info("Created market_price_enriched_event consumer endpoint for topic: {}", endpoint.getWorkflow());
        return endpoint;
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceEnrichedConsumer")
    public MarketPriceEnrichedConsumer marketPriceEnrichedConsumer(@Qualifier("marketPriceEnrichedConsumerEndpoint") KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> endpoint) {
        return new MarketPriceEnrichedConsumer(endpoint);
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceRawConsumer")
    public MarketPriceRawConsumer marketPriceRawConsumer(@Qualifier("marketPriceRawConsumerEndpoint") KafkaConsumerEndpoint<Long, MarketPriceDetail> endpoint) {
        return new MarketPriceRawConsumer(endpoint);
    }

    @Bean(name = "tsRepo")
    @ConditionalOnMissingBean(name = "tsRepo")
    public RedisTsRepo tsRepo(ClientResources clientResources, ClusterClientOptions clientOptions) {
        return new RedisTsRepo(RedisUtil.createLettuceConnectionFactories(
                redisProperties.getRedis().getTsData().getHost(),
                redisProperties.getRedis().getTsData().getPort(),
                redisProperties.getRedis().getClientName(),
                10,
                clientResources,
                clientOptions));
    }

    @Bean(name = "clientOptions")
    public ClusterClientOptions clientOptions() {
        return ClusterClientOptions.builder()
                .suspendReconnectOnProtocolFailure(true)
                .autoReconnect(true)
                .build();
    }

    @Bean(name = "clientResources")
    public static ClientResources getClientResources() {
        int cpuCount = Runtime.getRuntime().availableProcessors();
        return DefaultClientResources.builder()
                .ioThreadPoolSize(cpuCount * 2)
                .computationThreadPoolSize(cpuCount)
                .reconnectDelay(Delay.exponential(Duration.ofMillis(100), Duration.ofSeconds(30), 2, TimeUnit.MILLISECONDS))
                .build();
    }

}
